#!/bin/bash

# OLS Regression Environment Setup Script
# This script sets up the conda environment and launches Jupyter Lab

echo "Setting up OLS Regression Environment..."

# Check if conda is installed
if ! command -v conda &> /dev/null; then
    echo "Error: conda is not installed or not in PATH"
    echo "Please install <PERSON><PERSON><PERSON> or <PERSON>con<PERSON> first"
    exit 1
fi

# Create conda environment from environment.yml
echo "Creating conda environment 'ols-regression'..."
conda env create -f environment.yml

# Check if environment was created successfully
if [ $? -eq 0 ]; then
    echo "Environment created successfully!"
else
    echo "Environment already exists or creation failed. Updating instead..."
    conda env update -f environment.yml
fi

# Activate the environment
echo "Activating environment..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate ols-regression

# Install the kernel for Jupyter
echo "Installing IPython kernel..."
python -m ipykernel install --user --name ols-regression --display-name "OLS Regression (Python 3.11)"

echo "Setup complete!"
echo ""
echo "To use the environment:"
echo "1. conda activate ols-regression"
echo "2. jupyter lab"
echo ""
echo "Or run: ./run_notebook.sh"
