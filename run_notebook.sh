#!/bin/bash

# <PERSON><PERSON>t to activate conda environment and launch Jupyter Lab

echo "Activating OLS Regression environment and launching Jupyter Lab..."

# Check if conda is available
if ! command -v conda &> /dev/null; then
    echo "Error: conda is not installed or not in PATH"
    exit 1
fi

# Activate the environment
source $(conda info --base)/etc/profile.d/conda.sh
conda activate ols-regression

# Check if activation was successful
if [ $? -eq 0 ]; then
    echo "Environment activated successfully!"
    echo "Launching Jupyter Lab..."
    jupyter lab ols_regression.ipynb
else
    echo "Failed to activate environment. Please run setup.sh first."
    exit 1
fi
