#!/usr/bin/env python3
"""
Test script for OLS regression functionality
"""

import json
import pandas as pd
import numpy as np
import statsmodels.api as sm
from datetime import datetime, date

def get_csi_values():
    """
    Get CSI (Case-Shiller Index) values for time adjustment.
    Using actual Charlotte NC SA (single-family, all tiers, 2000 = 100) values from FRED.
    Source: S&P CoreLogic Case-Shiller NC-Charlotte Home Price Index (CRXRSA)
    """
    # Actual Charlotte NC Case-Shiller Index values (2000 = 100)
    # Source: https://fred.stlouisfed.org/series/CRXRSA
    csi_data = {
        '2024-10': 279.8,   # Estimated based on trend
        '2024-11': 281.95,  # Actual FRED data
        '2024-12': 283.19,  # Actual FRED data
        '2025-01': 284.06,  # Actual FRED data
        '2025-02': 284.39,  # Actual FRED data
        '2025-03': 283.55,  # Actual FRED data (current reference point)
        '2025-04': 284.2,   # Projected based on trend
        '2025-05': 285.0,   # Projected based on trend
        '2025-06': 285.8,   # Projected based on trend (t0)
    }
    return csi_data

def apply_time_adjustment(close_price, close_date, reference_date=None, csi_data=None):
    """
    Apply time adjustment to comparable sale prices using CSI.
    Formula: timeAdjPrice = comp_close_price × (CSI_t0 / CSI_tsale)

    Args:
        close_price: Original close price
        close_date: Date of the sale (string format: "YYYY-MM-DD HH:MM:SS")
        reference_date: Reference date for adjustment (defaults to current date)
        csi_data: Dictionary of CSI values by month

    Returns:
        Adjusted price
    """
    if csi_data is None:
        csi_data = get_csi_values()

    if reference_date is None:
        reference_date = "2025-03-01"  # Current reference point (latest actual data)

    try:
        # Parse the close date
        sale_date = datetime.strptime(close_date.split()[0], "%Y-%m-%d")
        ref_date = datetime.strptime(reference_date, "%Y-%m-%d")

        # Get month keys for CSI lookup
        sale_month_key = sale_date.strftime("%Y-%m")
        ref_month_key = ref_date.strftime("%Y-%m")

        # Get CSI values
        csi_sale = csi_data.get(sale_month_key)
        csi_ref = csi_data.get(ref_month_key)

        if csi_sale is None or csi_ref is None:
            print(f"Warning: CSI data not available for {sale_month_key} or {ref_month_key}, using unadjusted price")
            return close_price

        # Apply time adjustment formula
        adjusted_price = close_price * (csi_ref / csi_sale)

        return adjusted_price

    except Exception as e:
        print(f"Error in time adjustment for date {close_date}: {e}")
        return close_price

def extract_comp_features(market_comps, apply_time_adj=True):
    """
    Extract features from market comparables data with optional time adjustment

    Args:
        market_comps: List of comparable sales data
        apply_time_adj: Whether to apply time adjustment using CSI
    """
    comp_data = []
    csi_data = get_csi_values() if apply_time_adj else None

    for comp in market_comps:
        try:
            # Extract features
            area = comp.get('comp_property_area')
            bedrooms = comp.get('comp_property_bedrooms')
            baths_full = comp.get('comp_property_bathsFull')
            baths_half = comp.get('comp_property_bathsHalf')
            year_built = comp.get('comp_property_yearBuilt')
            close_price = comp.get('comp_close_price')
            close_date = comp.get('comp_close_date')

            # Convert to numeric and handle missing values
            if all(v is not None for v in [area, bedrooms, baths_full, baths_half, year_built, close_price]):
                area = float(area)
                bedrooms = int(bedrooms)
                baths_full = int(baths_full)
                baths_half = int(baths_half)
                year_built = int(year_built)
                close_price = float(close_price)

                # Apply time adjustment if requested and close_date is available
                if apply_time_adj and close_date:
                    adjusted_price = apply_time_adjustment(close_price, close_date, csi_data=csi_data)
                    original_price = close_price
                    close_price = adjusted_price
                else:
                    original_price = close_price

                # Calculate total bathrooms
                total_baths = baths_full + (baths_half * 0.5)

                comp_data.append({
                    'area': area,
                    'beds': bedrooms,
                    'baths': total_baths,
                    'yearBuilt': year_built,
                    'price': close_price,
                    'original_price': original_price,
                    'close_date': close_date,
                    'address': comp.get('comp_property_address', 'Unknown')
                })
        except (ValueError, TypeError) as e:
            print(f"Skipping comp due to data issue: {e}")
            continue
    
    return pd.DataFrame(comp_data)

def run_ols_regression(df):
    """Run OLS regression and return results in the specified format"""
    # Prepare the data
    X = df[['area', 'beds', 'baths', 'yearBuilt']].copy()
    y = df['price'].copy()
    
    # Add constant term for intercept
    X = sm.add_constant(X)
    
    # Fit the model
    model = sm.OLS(y, X).fit()
    
    # Extract results
    n_obs = int(model.nobs)
    
    # Get coefficients (excluding intercept)
    coeffs = {
        'area': round(model.params['area']),
        'beds': round(model.params['beds']),
        'baths': round(model.params['baths']),
        'yearBuilt': round(model.params['yearBuilt'])
    }
    
    # Get t-statistics (excluding intercept)
    t_stats = {
        'area': round(model.tvalues['area'], 1),
        'beds': round(model.tvalues['beds'], 1),
        'baths': round(model.tvalues['baths'], 1),
        'yearBuilt': round(model.tvalues['yearBuilt'], 1)
    }
    
    # Get R-squared
    r2 = round(model.rsquared, 2)
    
    # Create output in specified format
    ols_debug = {
        'nObs': n_obs,
        'coeffs': coeffs,
        'tStats': t_stats,
        'r2': r2
    }
    
    return ols_debug, model

def main():
    print("Testing OLS Regression...")
    
    # Load the JSON data
    try:
        with open('sample_data.json', 'r') as f:
            data = json.load(f)
        print(f"✓ Loaded data with {len(data['marketComps'])} market comps")
    except FileNotFoundError:
        print("✗ sample_data.json not found")
        return
    except json.JSONDecodeError:
        print("✗ Invalid JSON in sample_data.json")
        return
    
    # Extract comparable sales data with time adjustment
    print("Applying time adjustment using CSI...")
    df_comps = extract_comp_features(data['marketComps'], apply_time_adj=True)
    print(f"✓ Extracted {len(df_comps)} valid comparable sales with time adjustment")

    # Show time adjustment summary
    if len(df_comps) > 0:
        total_adjustment = df_comps['price'].sum() - df_comps['original_price'].sum()
        avg_adjustment_pct = ((df_comps['price'] / df_comps['original_price'] - 1) * 100).mean()
        print(f"✓ Average time adjustment: {avg_adjustment_pct:.2f}%")
        print(f"✓ Total price adjustment: ${total_adjustment:,.0f}")
    
    if len(df_comps) < 5:
        print(f"✗ Insufficient data for regression. Need at least 5 observations, have {len(df_comps)}")
        return
    
    # Run the regression
    try:
        ols_results, model = run_ols_regression(df_comps)
        print("✓ OLS regression completed successfully")
        
        print("\nResults:")
        print(json.dumps({"olsDebug": ols_results}, indent=2))
        
        # Save results
        output_data = {
            'olsDebug': ols_results,
            'dataInfo': {
                'totalComps': len(data['marketComps']),
                'validComps': len(df_comps),
                'dataSource': 'sample_data.json'
            }
        }
        
        with open('ols_results.json', 'w') as f:
            json.dump(output_data, f, indent=2)
        print("✓ Results saved to 'ols_results.json'")
        
    except Exception as e:
        print(f"✗ Error running regression: {e}")
        return

if __name__ == "__main__":
    main()
