#!/usr/bin/env python3
"""
Test script for paired-sales analysis functionality
Validates the core functions before running the full notebook
"""

import json
import pandas as pd
import numpy as np
import hashlib
from datetime import datetime

def get_csi_values():
    """
    Get CSI (Case-Shiller Index) values for Charlotte NC time adjustment.
    Using Charlotte NC SA (single-family, all tiers, 2000 = 100) format.
    """
    csi_data = {
        '2024-12': 278.2,
        '2025-01': 280.1,
        '2025-02': 281.7,
        '2025-03': 283.4,  # Reference point (t0)
        '2025-04': 284.9,
        '2025-05': 286.3,
        '2025-06': 287.8,
    }
    return csi_data

def apply_time_adjustment(close_price, close_date, reference_date=None, csi_data=None):
    """Apply time adjustment using CSI"""
    if csi_data is None:
        csi_data = get_csi_values()
    
    if reference_date is None:
        reference_date = "2025-03-01"
    
    try:
        sale_date = datetime.strptime(close_date.split()[0], "%Y-%m-%d")
        ref_date = datetime.strptime(reference_date, "%Y-%m-%d")
        
        sale_month_key = sale_date.strftime("%Y-%m")
        ref_month_key = ref_date.strftime("%Y-%m")
        
        csi_sale = csi_data.get(sale_month_key)
        csi_ref = csi_data.get(ref_month_key)
        
        if csi_sale is None or csi_ref is None:
            return close_price, 1.0, csi_ref, csi_sale
        
        factor = csi_ref / csi_sale
        adjusted_price = close_price * factor
        
        return adjusted_price, factor, csi_ref, csi_sale
        
    except Exception as e:
        print(f"Error in time adjustment: {e}")
        return close_price, 1.0, None, None

def extract_comp_features(market_comps, apply_time_adj=True):
    """Extract features from market comparables with time adjustment"""
    comp_data = []
    csi_data = get_csi_values() if apply_time_adj else None
    time_adj_audit = []
    
    for comp in market_comps:
        try:
            area = comp.get('comp_property_area')
            bedrooms = comp.get('comp_property_bedrooms')
            baths_full = comp.get('comp_property_bathsFull')
            baths_half = comp.get('comp_property_bathsHalf')
            year_built = comp.get('comp_property_yearBuilt')
            close_price = comp.get('comp_close_price')
            close_date = comp.get('comp_close_date')
            comp_id = comp.get('id')
            
            if all(v is not None for v in [area, bedrooms, baths_full, baths_half, year_built, close_price]):
                area = float(area)
                bedrooms = int(bedrooms)
                baths_full = int(baths_full)
                baths_half = int(baths_half)
                year_built = int(year_built)
                close_price = float(close_price)
                
                if apply_time_adj and close_date:
                    adjusted_price, factor, csi_ref, csi_sale = apply_time_adjustment(close_price, close_date, csi_data=csi_data)
                    original_price = close_price
                    close_price = adjusted_price
                    
                    if len(time_adj_audit) < 3 and csi_sale is not None:
                        time_adj_audit.append({
                            "id": comp_id,
                            "csiSale": csi_sale,
                            "factor": round(factor, 3)
                        })
                else:
                    original_price = close_price
                
                total_baths = baths_full + (baths_half * 0.5)
                
                comp_data.append({
                    'id': comp_id,
                    'area': area,
                    'beds': bedrooms,
                    'baths': total_baths,
                    'yearBuilt': year_built,
                    'price': close_price,
                    'original_price': original_price,
                    'close_date': close_date,
                    'address': comp.get('comp_property_address', 'Unknown')
                })
        except (ValueError, TypeError) as e:
            print(f"Skipping comp due to data issue: {e}")
            continue
    
    return pd.DataFrame(comp_data), time_adj_audit

def generate_pairs_hash(pairs):
    """Generate MD5 hash of concatenated sorted pair IDs + deltas"""
    if not pairs:
        return "no_pairs"
    
    sorted_pairs = sorted(pairs, key=lambda x: (x['feature'], x['compA'], x['compB']))
    
    hash_string = ""
    for pair in sorted_pairs:
        hash_string += f"{pair['compA']}{pair['compB']}{pair['Δfeature']}{pair['Δprice']}"
    
    return hashlib.md5(hash_string.encode()).hexdigest()[:8]

def main():
    print("🏠 PAIRED-SALES ANALYSIS TEST")
    print("=" * 40)
    
    # Load data
    try:
        with open('sample_data.json', 'r') as f:
            data = json.load(f)
        print(f"✓ Loaded {len(data['marketComps'])} market comparables")
    except FileNotFoundError:
        print("✗ sample_data.json not found")
        return
    
    # Test CSI data
    csi_data = get_csi_values()
    print(f"✓ CSI data loaded: {len(csi_data)} months")
    
    # Test time adjustment
    test_price = 400000
    test_date = "2025-02-28 00:00:00"
    adjusted_price, factor, csi_ref, csi_sale = apply_time_adjustment(test_price, test_date)
    print(f"✓ Time adjustment test: ${test_price:,} → ${adjusted_price:,.0f} (factor: {factor:.4f})")
    
    # Extract comparable sales
    df_comps, time_adj_audit = extract_comp_features(data['marketComps'], apply_time_adj=True)
    print(f"✓ Extracted {len(df_comps)} valid comparable sales")
    
    if len(df_comps) > 0:
        total_adjustment = df_comps['price'].sum() - df_comps['original_price'].sum()
        avg_adjustment_pct = ((df_comps['price'] / df_comps['original_price'] - 1) * 100).mean()
        print(f"✓ Average time adjustment: {avg_adjustment_pct:.2f}%")
        print(f"✓ Total price adjustment: ${total_adjustment:,.0f}")
    
    # Test hash generation
    sample_pairs = [
        {'feature': 'beds', 'compA': 'C1', 'compB': 'C2', 'Δfeature': 1, 'Δprice': 15000},
        {'feature': 'area', 'compA': 'C3', 'compB': 'C4', 'Δfeature': 100, 'Δprice': 12000}
    ]
    pairs_hash = generate_pairs_hash(sample_pairs)
    print(f"✓ Pairs hash generation: {pairs_hash}")
    
    # Create sample output structure
    output = {
        "modelInfo": {
            "method": "paired_sales",
            "unitValues": {"area": 120, "beds": 15000, "baths": 10000, "yearBuilt": 500},
            "pairExamples": sample_pairs,
            "pairsHash": pairs_hash
        },
        "csiSeries": "Charlotte NC SA (single-family, all tiers, 2000 = 100)",
        "timeAdjAudit": {
            "csiValue_t0": 283.4,
            "sample": time_adj_audit[:3]
        }
    }
    
    print(f"\n📋 Sample output structure:")
    print(json.dumps(output, indent=2))
    
    # Save test results
    with open('paired_sales_test_results.json', 'w') as f:
        json.dump(output, f, indent=2)
    
    print(f"\n✅ Test completed successfully!")
    print(f"💾 Results saved to 'paired_sales_test_results.json'")

if __name__ == "__main__":
    main()
