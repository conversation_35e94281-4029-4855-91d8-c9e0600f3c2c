#!/usr/bin/env python3
"""
Demo script showing paired-sales analysis with the exact output format requested
"""

import json

def main():
    print("🏠 PAIRED-SALES ANALYSIS DEMO")
    print("=" * 50)
    print()
    
    print("This demo shows the paired-sales analysis following the logic in prompts.md")
    print("with the exact output format requested:")
    print()
    
    # Load the test results to show the format
    try:
        with open('paired_sales_test_results.json', 'r') as f:
            results = json.load(f)
        
        print("📋 PAIRED-SALES OUTPUT FORMAT:")
        print("=" * 40)
        print()
        
        # Show the exact format requested
        output_format = {
            "modelInfo": {
                "method": "paired_sales",
                "unitValues": { "area": 0, "beds": 0, "baths": 0, "yearBuilt": 0 },
                "pairExamples": [
                    { "feature": "string", "compA": "string", "compB": "string",
                      "Δfeature": 0, "Δprice": 0 }
                ],
                "pairsHash": "string"
            },
            "csiSeries": "string",
            "timeAdjAudit": {
                "csiValue_t0": 0,
                "sample": [
                    { "id": "string", "csiSale": 0, "factor": 0.000 }
                ]
            }
        }
        
        print("EXPECTED FORMAT:")
        print(json.dumps(output_format, indent=2))
        print()
        
        print("ACTUAL RESULTS FROM TEST:")
        print(json.dumps(results, indent=2))
        print()
        
        print("✅ KEY FEATURES IMPLEMENTED:")
        print("  ✓ Time adjustment using Charlotte NC CSI")
        print("  ✓ Deterministic pair selection algorithm")
        print("  ✓ Unit value calculation using median ratios")
        print("  ✓ Relative weight caps (baths ≤ 0.7×beds, yearBuilt ≤ 0.5×beds)")
        print("  ✓ MD5 hash generation for pairs")
        print("  ✓ CSI audit trail with factors")
        print("  ✓ Exact JSON output format as specified")
        print()
        
        print("📊 ANALYSIS DETAILS:")
        print(f"  • Method: {results['modelInfo']['method']}")
        print(f"  • CSI Series: {results['csiSeries']}")
        print(f"  • Pairs Hash: {results['modelInfo']['pairsHash']}")
        print(f"  • CSI Reference (t0): {results['timeAdjAudit']['csiValue_t0']}")
        print(f"  • Sample Adjustments: {len(results['timeAdjAudit']['sample'])} comps")
        print()
        
        print("🔧 HOW TO USE:")
        print("  1. Open paired_sales_analysis.ipynb in Jupyter")
        print("  2. Run all cells to perform the analysis")
        print("  3. Results will be saved to 'paired_sales_results.json'")
        print("  4. Output format matches API specification exactly")
        print()
        
        print("📝 NOTEBOOK FEATURES:")
        print("  • Interactive step-by-step analysis")
        print("  • Detailed explanations of each step")
        print("  • Time adjustment with CSI data")
        print("  • Paired-sales algorithm implementation")
        print("  • JSON output generation")
        print("  • Analysis summary and validation")
        
    except FileNotFoundError:
        print("⚠️  Test results not found. Run test_paired_sales.py first.")
        return
    
    print()
    print("✅ Demo completed! The paired-sales analysis notebook is ready to use.")

if __name__ == "__main__":
    main()
