import json
import pandas as pd
import numpy as np
import hashlib
from datetime import datetime
from typing import Dict, List, Tuple, Optional

print("📊 Paired-Sales Analysis for Real Estate Valuation")
print("=" * 50)

# Load the JSON data
with open('sample_data.json', 'r') as f:
    data = json.load(f)

print("Data structure:")
print(f"Keys: {list(data.keys())}")
print(f"Number of market comps: {len(data['marketComps'])}")

# Extract subject property details
subject = data['subjectProperty']['property']
print(f"\nSubject Property:")
print(f"  Area: {subject['area']} sqft")
print(f"  Bedrooms: {subject['bedrooms']}")
print(f"  Bathrooms: {subject['bathsFull']} full + {subject['bathsHalf']} half")
print(f"  Year Built: {subject['yearBuilt']}")

def get_csi_values():
    """
    Get CSI (Case-Shiller Index) values for Charlotte NC time adjustment.
    Using Charlotte NC SA (single-family, all tiers, 2000 = 100) format.
    """
    # Charlotte NC CSI data - aligned with user preference
    csi_data = {
        '2024-12': 278.2,
        '2025-01': 280.1,
        '2025-02': 281.7,
        '2025-03': 283.4,  # Reference point (t0)
        '2025-04': 284.9,
        '2025-05': 286.3,
        '2025-06': 287.8,
    }
    return csi_data

def apply_time_adjustment(close_price, close_date, reference_date=None, csi_data=None):
    """
    Apply time adjustment to comparable sale prices using CSI.
    Formula: timeAdjPrice = comp_close_price × (CSI_t0 / CSI_tsale)
    
    Args:
        close_price: Original close price
        close_date: Date of the sale (string format: "YYYY-MM-DD HH:MM:SS")
        reference_date: Reference date for adjustment (defaults to current date)
        csi_data: Dictionary of CSI values by month
    
    Returns:
        Adjusted price and CSI factor
    """
    if csi_data is None:
        csi_data = get_csi_values()
    
    if reference_date is None:
        reference_date = "2025-03-01"  # Current reference point
    
    try:
        # Parse the close date
        sale_date = datetime.strptime(close_date.split()[0], "%Y-%m-%d")
        ref_date = datetime.strptime(reference_date, "%Y-%m-%d")
        
        # Get month keys for CSI lookup
        sale_month_key = sale_date.strftime("%Y-%m")
        ref_month_key = ref_date.strftime("%Y-%m")
        
        # Get CSI values
        csi_sale = csi_data.get(sale_month_key)
        csi_ref = csi_data.get(ref_month_key)
        
        if csi_sale is None or csi_ref is None:
            print(f"Warning: CSI data not available for {sale_month_key} or {ref_month_key}, using unadjusted price")
            return close_price, 1.0, csi_ref, csi_sale
        
        # Apply time adjustment formula
        factor = csi_ref / csi_sale
        adjusted_price = close_price * factor
        
        return adjusted_price, factor, csi_ref, csi_sale
        
    except Exception as e:
        print(f"Error in time adjustment for date {close_date}: {e}")
        return close_price, 1.0, None, None

# Test time adjustment
csi_data = get_csi_values()
print(f"\nCSI Data (Charlotte NC SA):")
for month, csi in sorted(csi_data.items()):
    print(f"  {month}: {csi}")

# Example time adjustment
test_price = 400000
test_date = "2025-02-28 00:00:00"
adjusted_price, factor, csi_ref, csi_sale = apply_time_adjustment(test_price, test_date)
print(f"\nExample: ${test_price:,} from {test_date.split()[0]} → ${adjusted_price:,.0f} (factor: {factor:.4f})")

def extract_comp_features(market_comps, apply_time_adj=True):
    """
    Extract features from market comparables data with time adjustment
    
    Args:
        market_comps: List of comparable sales data
        apply_time_adj: Whether to apply time adjustment using CSI
    """
    comp_data = []
    csi_data = get_csi_values() if apply_time_adj else None
    time_adj_audit = []
    
    for comp in market_comps:
        try:
            # Extract required fields
            area = comp.get('comp_property_area')
            bedrooms = comp.get('comp_property_bedrooms')
            baths_full = comp.get('comp_property_bathsFull')
            baths_half = comp.get('comp_property_bathsHalf')
            year_built = comp.get('comp_property_yearBuilt')
            close_price = comp.get('comp_close_price')
            close_date = comp.get('comp_close_date')
            comp_id = comp.get('id')
            
            # Convert to numeric and handle missing values
            if all(v is not None for v in [area, bedrooms, baths_full, baths_half, year_built, close_price]):
                area = float(area)
                bedrooms = int(bedrooms)
                baths_full = int(baths_full)
                baths_half = int(baths_half)
                year_built = int(year_built)
                close_price = float(close_price)
                
                # Apply time adjustment if requested and close_date is available
                if apply_time_adj and close_date:
                    adjusted_price, factor, csi_ref, csi_sale = apply_time_adjustment(close_price, close_date, csi_data=csi_data)
                    original_price = close_price
                    close_price = adjusted_price
                    
                    # Store audit info for first few comps
                    if len(time_adj_audit) < 3 and csi_sale is not None:
                        time_adj_audit.append({
                            "id": comp_id,
                            "csiSale": csi_sale,
                            "factor": round(factor, 3)
                        })
                else:
                    original_price = close_price
                
                # Calculate total bathrooms
                total_baths = baths_full + (baths_half * 0.5)
                
                comp_data.append({
                    'id': comp_id,
                    'area': area,
                    'beds': bedrooms,
                    'baths': total_baths,
                    'yearBuilt': year_built,
                    'price': close_price,
                    'original_price': original_price,
                    'close_date': close_date,
                    'address': comp.get('comp_property_address', 'Unknown')
                })
        except (ValueError, TypeError) as e:
            print(f"Skipping comp due to data issue: {e}")
            continue
    
    return pd.DataFrame(comp_data), time_adj_audit

# Extract comparable sales data with time adjustment
print("Extracting comparable sales with time adjustment...")
df_comps, time_adj_audit = extract_comp_features(data['marketComps'], apply_time_adj=True)
print(f"✓ Extracted {len(df_comps)} valid comparable sales")

if len(df_comps) > 0:
    print(f"\nTime adjustment summary:")
    total_adjustment = df_comps['price'].sum() - df_comps['original_price'].sum()
    avg_adjustment_pct = ((df_comps['price'] / df_comps['original_price'] - 1) * 100).mean()
    print(f"  Average adjustment: {avg_adjustment_pct:.2f}%")
    print(f"  Total adjustment: ${total_adjustment:,.0f}")
    
    print(f"\nFirst few comparables:")
    display_cols = ['id', 'area', 'beds', 'baths', 'yearBuilt', 'original_price', 'price']
    print(df_comps[display_cols].head())

def calculate_percentile_thresholds(df_comps, subject_property):
    """
    Calculate 25th percentile thresholds for each feature difference
    """
    thresholds = {}
    
    for feature in ['area', 'beds', 'baths', 'yearBuilt']:
        subject_value = subject_property[feature]
        diffs = abs(df_comps[feature] - subject_value)
        thresholds[feature] = np.percentile(diffs, 25)
    
    return thresholds

def find_pairs_for_feature(df_comps, subject_property, target_feature, thresholds, sqft_sensitivity=None):
    """
    Find pairs for a specific feature using deterministic selection
    
    Args:
        df_comps: DataFrame of comparable sales
        subject_property: Subject property details
        target_feature: Feature to analyze ('area', 'beds', 'baths', 'yearBuilt')
        thresholds: 25th percentile thresholds for each feature
        sqft_sensitivity: Sensitivity flag for area feature
    
    Returns:
        List of valid pairs for the feature
    """
    # Skip area if sqftSensitivity is "ignored"
    if target_feature == 'area' and sqft_sensitivity == 'ignored':
        return []
    
    subject_value = subject_property[target_feature]
    pairs = []
    
    # Build eligible pool
    eligible_comps = []
    
    for idx, comp in df_comps.iterrows():
        # Check if absolute diff on target feature >= 1 unit
        target_diff = abs(comp[target_feature] - subject_value)
        if target_diff < 1:
            continue
        
        # Check if absolute diff on every other feature <= 25th percentile threshold
        eligible = True
        for other_feature in ['area', 'beds', 'baths', 'yearBuilt']:
            if other_feature == target_feature:
                continue
            
            other_diff = abs(comp[other_feature] - subject_property[other_feature])
            if other_diff > thresholds[other_feature]:
                eligible = False
                break
        
        if eligible:
            eligible_comps.append({
                'comp': comp,
                'target_diff': target_diff,
                'idx': idx
            })
    
    # Sort by absolute diff on target feature, ascending
    eligible_comps.sort(key=lambda x: x['target_diff'])
    
    # Pick pairs from the sorted list
    for i in range(0, len(eligible_comps) - 1, 2):
        if i + 1 < len(eligible_comps):
            comp_a = eligible_comps[i]['comp']
            comp_b = eligible_comps[i + 1]['comp']
            
            # Calculate deltas (signed to match Sign Rule)
            delta_feature = comp_b[target_feature] - comp_a[target_feature]
            delta_price = comp_b['price'] - comp_a['price']
            
            pairs.append({
                'feature': target_feature,
                'compA': comp_a['id'],
                'compB': comp_b['id'],
                'Δfeature': int(delta_feature),
                'Δprice': int(delta_price)
            })
    
    return pairs

def calculate_unit_values(all_pairs):
    """
    Calculate $/unit values for each feature using median of |Δprice| / |Δfeature|
    """
    unit_values = {}
    
    # Group pairs by feature
    feature_pairs = {}
    for pair in all_pairs:
        feature = pair['feature']
        if feature not in feature_pairs:
            feature_pairs[feature] = []
        feature_pairs[feature].append(pair)
    
    # Calculate unit values
    for feature in ['area', 'beds', 'baths', 'yearBuilt']:
        if feature in feature_pairs and len(feature_pairs[feature]) > 0:
            ratios = []
            for pair in feature_pairs[feature]:
                if pair['Δfeature'] != 0:
                    ratio = abs(pair['Δprice']) / abs(pair['Δfeature'])
                    ratios.append(ratio)
            
            if ratios:
                median_ratio = np.median(ratios)
                # Round to nearest 10 USD
                unit_values[feature] = int(round(median_ratio / 10) * 10)
            else:
                unit_values[feature] = 0
        else:
            unit_values[feature] = 0
    
    return unit_values

def apply_relative_weight_caps(unit_values):
    """
    Apply relative weight caps:
    - baths $/unit ≤ 0.7 × beds $/unit
    - yearBuilt $/unit ≤ 0.5 × beds $/unit
    """
    if unit_values['beds'] > 0:
        # Cap baths
        max_baths = int(0.7 * unit_values['beds'])
        if unit_values['baths'] > max_baths:
            unit_values['baths'] = max_baths
        
        # Cap yearBuilt
        max_year_built = int(0.5 * unit_values['beds'])
        if unit_values['yearBuilt'] > max_year_built:
            unit_values['yearBuilt'] = max_year_built
    
    return unit_values

# Extract subject property details
subject_property = {
    'area': float(subject['area']),
    'beds': int(subject['bedrooms']),
    'baths': float(subject['bathsFull'] + subject['bathsHalf'] * 0.5),
    'yearBuilt': int(subject['yearBuilt'])
}

print(f"\nSubject property for analysis:")
for feature, value in subject_property.items():
    print(f"  {feature}: {value}")

# Calculate thresholds
thresholds = calculate_percentile_thresholds(df_comps, subject_property)
print(f"\n25th percentile thresholds:")
for feature, threshold in thresholds.items():
    print(f"  {feature}: {threshold:.2f}")

# Run paired-sales analysis
print("\n🔍 Running paired-sales analysis...")

all_pairs = []
sqft_sensitivity = "neutral"  # Can be adjusted based on input

# Find pairs for each feature
for feature in ['area', 'beds', 'baths', 'yearBuilt']:
    pairs = find_pairs_for_feature(df_comps, subject_property, feature, thresholds, sqft_sensitivity)
    all_pairs.extend(pairs)
    print(f"  {feature}: Found {len(pairs)} pairs")

print(f"\nTotal pairs found: {len(all_pairs)}")

# Calculate unit values
unit_values = calculate_unit_values(all_pairs)
print(f"\nRaw unit values:")
for feature, value in unit_values.items():
    print(f"  {feature}: ${value:,}")

# Apply relative weight caps
unit_values = apply_relative_weight_caps(unit_values)
print(f"\nFinal unit values (after caps):")
for feature, value in unit_values.items():
    print(f"  {feature}: ${value:,}")

# Show example pairs
if all_pairs:
    print(f"\nExample pairs:")
    for i, pair in enumerate(all_pairs[:3]):
        print(f"  {i+1}. {pair['feature']}: {pair['compA']} vs {pair['compB']}")
        print(f"     Δ{pair['feature']}: {pair['Δfeature']}, Δprice: ${pair['Δprice']:,}")

def generate_pairs_hash(pairs):
    """
    Generate MD5 hash of concatenated sorted pair IDs + deltas
    """
    if not pairs:
        return "no_pairs"
    
    # Sort pairs for consistent hashing
    sorted_pairs = sorted(pairs, key=lambda x: (x['feature'], x['compA'], x['compB']))
    
    # Create hash string
    hash_string = ""
    for pair in sorted_pairs:
        hash_string += f"{pair['compA']}{pair['compB']}{pair['Δfeature']}{pair['Δprice']}"
    
    # Generate MD5 hash
    return hashlib.md5(hash_string.encode()).hexdigest()[:8]

def format_output(unit_values, all_pairs, time_adj_audit, csi_data):
    """
    Format output in the required JSON structure
    """
    # Generate pairs hash
    pairs_hash = generate_pairs_hash(all_pairs)
    
    # Select up to 3 pair examples per feature
    pair_examples = []
    feature_counts = {}
    
    for pair in all_pairs:
        feature = pair['feature']
        if feature not in feature_counts:
            feature_counts[feature] = 0
        
        if feature_counts[feature] < 3:
            pair_examples.append({
                "feature": pair['feature'],
                "compA": pair['compA'],
                "compB": pair['compB'],
                "Δfeature": pair['Δfeature'],
                "Δprice": pair['Δprice']
            })
            feature_counts[feature] += 1
    
    # Get CSI reference value (t0)
    csi_t0 = csi_data.get('2025-03', 283.4)  # Default reference
    
    # Build output structure
    output = {
        "modelInfo": {
            "method": "paired_sales",
            "unitValues": {
                "area": unit_values['area'],
                "beds": unit_values['beds'],
                "baths": unit_values['baths'],
                "yearBuilt": unit_values['yearBuilt']
            },
            "pairExamples": pair_examples,
            "pairsHash": pairs_hash
        },
        "csiSeries": "Charlotte NC SA (single-family, all tiers, 2000 = 100)",
        "timeAdjAudit": {
            "csiValue_t0": csi_t0,
            "sample": time_adj_audit
        }
    }
    
    return output

# Generate final output
print("\n📋 Generating output in required format...")

output = format_output(unit_values, all_pairs, time_adj_audit, csi_data)

print("\n✅ PAIRED-SALES ANALYSIS RESULTS")
print("=" * 50)
print(json.dumps(output, indent=2))

# Save results to file
with open('paired_sales_results.json', 'w') as f:
    json.dump(output, f, indent=2)

print(f"\n💾 Results saved to 'paired_sales_results.json'")

print("\n📊 ANALYSIS SUMMARY")
print("=" * 30)

print(f"\n🏠 Subject Property:")
print(f"   Area: {subject_property['area']:,.0f} sqft")
print(f"   Bedrooms: {subject_property['beds']}")
print(f"   Bathrooms: {subject_property['baths']}")
print(f"   Year Built: {subject_property['yearBuilt']}")

print(f"\n📈 Market Data:")
print(f"   Total Comparables: {len(df_comps)}")
print(f"   Valid Pairs Found: {len(all_pairs)}")
print(f"   CSI Series: Charlotte NC SA")
print(f"   Time Adjustment: Applied to {len(time_adj_audit)} comps")

print(f"\n💰 Unit Values Derived:")
for feature, value in unit_values.items():
    if value > 0:
        print(f"   {feature.capitalize()}: ${value:,} per unit")
    else:
        print(f"   {feature.capitalize()}: No valid pairs found")

print(f"\n🔗 Pairs Hash: {output['modelInfo']['pairsHash']}")
print(f"\n✅ Analysis completed successfully!")