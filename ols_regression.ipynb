{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# OLS Regression Analysis for Real Estate Data\n",
    "\n",
    "This notebook performs Ordinary Least Squares (OLS) regression on real estate market data to predict property prices based on key features:\n",
    "- Area (square feet)\n",
    "- Number of bedrooms\n",
    "- Number of bathrooms (full + half)\n",
    "- Year built\n",
    "\n",
    "The output format matches the required specification with coefficients, t-statistics, R², and number of observations."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import json\n",
    "import pandas as pd\n",
    "import numpy as np\n",
    "import statsmodels.api as sm\n",
    "from statsmodels.stats.diagnostic import het_breuschpagan\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "from datetime import datetime, date\n",
    "\n",
    "# Set display options\n",
    "pd.set_option('display.max_columns', None)\n",
    "pd.set_option('display.width', None)\n",
    "pd.set_option('display.max_colwidth', None)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. Load and Examine Data"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load the JSON data\n",
    "with open('sample_data.json', 'r') as f:\n",
    "    data = json.load(f)\n",
    "\n",
    "print(\"Data structure:\")\n",
    "print(f\"Keys: {list(data.keys())}\")\n",
    "print(f\"Number of market comps: {len(data['marketComps'])}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "    "## 2. Time Adjustment Functions\\n",\n    "\\n",\n    "Before running OLS regression, we apply time adjustment to comparable sales using CSI (Case-Shiller Index).\\n",\n    "Formula: `timeAdjPrice = comp_close_price × (CSI_t0 / CSI_tsale)`""
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def extract_comp_features(market_comps):\n",
    "    \"\"\"\n",
    "    Extract features from market comparables data\n",
    "    \"\"\"\n",
    "    comp_data = []\n",
    "    \n",
    "    for comp in market_comps:\n",
    "        try:\n",
    "            # Extract features\n",
    "            area = comp.get('comp_property_area')\n",
    "            bedrooms = comp.get('comp_property_bedrooms')\n",
    "            baths_full = comp.get('comp_property_bathsFull')\n",
    "            baths_half = comp.get('comp_property_bathsHalf')\n",
    "            year_built = comp.get('comp_property_yearBuilt')\n",
    "            close_price = comp.get('comp_close_price')\n",
    "            \n",
    "            # Convert to numeric and handle missing values\n",
    "            if all(v is not None for v in [area, bedrooms, baths_full, baths_half, year_built, close_price]):\n",
    "                area = float(area)\n",
    "                bedrooms = int(bedrooms)\n",
    "                baths_full = int(baths_full)\n",
    "                baths_half = int(baths_half)\n",
    "                year_built = int(year_built)\n",
    "                close_price = float(close_price)\n",
    "                \n",
    "                # Calculate total bathrooms\n",
    "                total_baths = baths_full + (baths_half * 0.5)\n",
    "                \n",
    "                comp_data.append({\n",
    "                    'area': area,\n",
    "                    'beds': bedrooms,\n",
    "                    'baths': total_baths,\n",
    "                    'yearBuilt': year_built,\n",
    "                    'price': close_price,\n",
    "                    'address': comp.get('comp_property_address', 'Unknown')\n",
    "                })\n",
    "        except (ValueError, TypeError) as e:\n",
    "            print(f\"Skipping comp due to data issue: {e}\")\n",
    "            continue\n",
    "    \n",
    "    return pd.DataFrame(comp_data)\n",
    "\n",
    "# Extract comparable sales data\n",
    "df_comps = extract_comp_features(data['marketComps'])\n",
    "print(f\"Extracted {len(df_comps)} valid comparable sales\")\n",
    "print(\"\\nFirst few records:\")\n",
    "print(df_comps.head())"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. Data Quality Check and Summary Statistics"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Check for missing values and data quality\n",
    "print(\"Data Quality Check:\")\n",
    "print(f\"Shape: {df_comps.shape}\")\n",
    "print(f\"Missing values:\\n{df_comps.isnull().sum()}\")\n",
    "print(f\"\\nData types:\\n{df_comps.dtypes}\")\n",
    "\n",
    "# Summary statistics\n",
    "print(\"\\nSummary Statistics:\")\n",
    "print(df_comps.describe())"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. Run OLS Regression"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def run_ols_regression(df):\n",
    "    \"\"\"\n",
    "    Run OLS regression and return results in the specified format\n",
    "    \"\"\"\n",
    "    # Prepare the data\n",
    "    X = df[['area', 'beds', 'baths', 'yearBuilt']].copy()\n",
    "    y = df['price'].copy()\n",
    "    \n",
    "    # Add constant term for intercept\n",
    "    X = sm.add_constant(X)\n",
    "    \n",
    "    # Fit the model\n",
    "    model = sm.OLS(y, X).fit()\n",
    "    \n",
    "    # Extract results\n",
    "    n_obs = int(model.nobs)\n",
    "    \n",
    "    # Get coefficients (excluding intercept)\n",
    "    coeffs = {\n",
    "        'area': round(model.params['area']),\n",
    "        'beds': round(model.params['beds']),\n",
    "        'baths': round(model.params['baths']),\n",
    "        'yearBuilt': round(model.params['yearBuilt'])\n",
    "    }\n",
    "    \n",
    "    # Get t-statistics (excluding intercept)\n",
    "    t_stats = {\n",
    "        'area': round(model.tvalues['area'], 1),\n",
    "        'beds': round(model.tvalues['beds'], 1),\n",
    "        'baths': round(model.tvalues['baths'], 1),\n",
    "        'yearBuilt': round(model.tvalues['yearBuilt'], 1)\n",
    "    }\n",
    "    \n",
    "    # Get R-squared\n",
    "    r2 = round(model.rsquared, 2)\n",
    "    \n",
    "    # Create output in specified format\n",
    "    ols_debug = {\n",
    "        'nObs': n_obs,\n",
    "        'coeffs': coeffs,\n",
    "        'tStats': t_stats,\n",
    "        'r2': r2\n",
    "    }\n",
    "    \n",
    "    return ols_debug, model\n",
    "\n",
    "# Run the regression\n",
    "if len(df_comps) >= 5:  # Need minimum observations for regression\n",
    "    ols_results, model = run_ols_regression(df_comps)\n",
    "    \n",
    "    print(\"OLS Regression Results:\")\n",
    "    print(json.dumps(ols_results, indent=2))\n",
    "else:\n",
    "    print(f\"Insufficient data for regression. Need at least 5 observations, have {len(df_comps)}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. Detailed Model Summary"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Display detailed model summary\n",
    "if 'model' in locals():\n",
    "    print(\"Detailed Model Summary:\")\n",
    "    print(model.summary())\n",
    "    \n",
    "    # Additional diagnostics\n",
    "    print(\"\\nModel Diagnostics:\")\n",
    "    print(f\"AIC: {model.aic:.2f}\")\n",
    "    print(f\"BIC: {model.bic:.2f}\")\n",
    "    print(f\"F-statistic: {model.fvalue:.2f}\")\n",
    "    print(f\"Prob (F-statistic): {model.f_pvalue:.4f}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 6. Visualization"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create visualizations\n",
    "if 'model' in locals():\n",
    "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n",
    "    \n",
    "    # Actual vs Predicted\n",
    "    y_pred = model.fittedvalues\n",
    "    y_actual = model.model.endog\n",
    "    \n",
    "    axes[0, 0].scatter(y_actual, y_pred, alpha=0.7)\n",
    "    axes[0, 0].plot([y_actual.min(), y_actual.max()], [y_actual.min(), y_actual.max()], 'r--', lw=2)\n",
    "    axes[0, 0].set_xlabel('Actual Price')\n",
    "    axes[0, 0].set_ylabel('Predicted Price')\n",
    "    axes[0, 0].set_title('Actual vs Predicted Prices')\n",
    "    \n",
    "    # Residuals vs Fitted\n",
    "    residuals = model.resid\n",
    "    axes[0, 1].scatter(y_pred, residuals, alpha=0.7)\n",
    "    axes[0, 1].axhline(y=0, color='r', linestyle='--')\n",
    "    axes[0, 1].set_xlabel('Fitted Values')\n",
    "    axes[0, 1].set_ylabel('Residuals')\n",
    "    axes[0, 1].set_title('Residuals vs Fitted Values')\n",
    "    \n",
    "    # Q-Q plot\n",
    "    from scipy import stats\n",
    "    stats.probplot(residuals, dist=\"norm\", plot=axes[1, 0])\n",
    "    axes[1, 0].set_title('Q-Q Plot of Residuals')\n",
    "    \n",
    "    # Feature correlation heatmap\n",
    "    corr_matrix = df_comps[['area', 'beds', 'baths', 'yearBuilt', 'price']].corr()\n",
    "    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, ax=axes[1, 1])\n",
    "    axes[1, 1].set_title('Feature Correlation Matrix')\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    plt.show()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 7. Export Results"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Save results to JSON file\n",
    "if 'ols_results' in locals():\n",
    "    output_data = {\n",
    "        'olsDebug': ols_results,\n",
    "        'dataInfo': {\n",
    "            'totalComps': len(data['marketComps']),\n",
    "            'validComps': len(df_comps),\n",
    "            'dataSource': 'sample_data.json'\n",
    "        }\n",
    "    }\n",
    "    \n",
    "    with open('ols_results.json', 'w') as f:\n",
    "        json.dump(output_data, f, indent=2)\n",
    "    \n",
    "    print(\"Results saved to 'ols_results.json'\")\n",
    "    print(\"\\nFinal Output:\")\n",
    "    print(json.dumps(ols_results, indent=2))"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3 (ipykernel)",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.11.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
