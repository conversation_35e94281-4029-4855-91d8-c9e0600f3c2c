#!/usr/bin/env python3
"""
Information about the Charlotte NC Case-Shiller Index data used for time adjustment
"""

from test_ols import get_csi_values

def show_csi_info():
    print("=" * 80)
    print("CHARLOTTE NC CASE-SHILLER INDEX DATA")
    print("=" * 80)
    
    print("📊 DATA SOURCE:")
    print("  Series: S&P CoreLogic Case-Shiller NC-Charlotte Home Price Index")
    print("  FRED Code: CRXRSA")
    print("  URL: https://fred.stlouisfed.org/series/CRXRSA")
    print("  Base: January 2000 = 100")
    print("  Frequency: Monthly, Seasonally Adjusted")
    print("  Coverage: Single-family homes, all price tiers")
    
    print("\n📈 CSI VALUES USED:")
    csi_data = get_csi_values()
    
    actual_data_months = ['2024-11', '2024-12', '2025-01', '2025-02', '2025-03']
    
    for month, csi in sorted(csi_data.items()):
        if month in actual_data_months:
            status = "[Actual FRED data]"
            if month == '2025-03':
                status += " ← Reference point (t0)"
        else:
            status = "[Projected/Estimated]"
        
        print(f"  {month}: {csi:7.2f} {status}")
    
    print("\n🔍 RECENT CHARLOTTE MARKET TRENDS:")
    print("  • March 2025: 283.55 (slight decline from February)")
    print("  • February 2025: 284.39 (peak recent value)")
    print("  • January 2025: 284.06 (strong start to year)")
    print("  • December 2024: 283.19 (year-end value)")
    print("  • November 2024: 281.95 (steady growth)")
    
    print("\n💡 TIME ADJUSTMENT IMPACT:")
    print("  • Sales from Jan-Feb 2025: Slight downward adjustment (-0.18% to -0.30%)")
    print("  • Sales from Mar 2025: No adjustment (reference month)")
    print("  • Sales from Apr-May 2025: Slight upward adjustment (+0.23% to +0.77%)")
    print("  • Overall effect: Minimal due to recent market stability")
    
    print("\n📋 METHODOLOGY:")
    print("  Formula: timeAdjPrice = comp_close_price × (CSI_t0 / CSI_tsale)")
    print("  Where:")
    print("    CSI_t0 = 283.55 (March 2025 reference)")
    print("    CSI_tsale = Index value at time of comparable sale")
    
    print("\n✅ DATA QUALITY:")
    print("  • Official S&P CoreLogic Case-Shiller data")
    print("  • Seasonally adjusted for accurate comparisons")
    print("  • Covers Charlotte metropolitan statistical area")
    print("  • Updated monthly with ~2 month lag")
    print("  • Industry standard for real estate price indexing")

if __name__ == "__main__":
    show_csi_info()
