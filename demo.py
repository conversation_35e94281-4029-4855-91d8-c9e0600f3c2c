#!/usr/bin/env python3
"""
Demo script showing the OLS regression results in the exact format requested
"""

import json

def main():
    print("=" * 60)
    print("OLS REGRESSION DEMO - REAL ESTATE DATA ANALYSIS")
    print("=" * 60)
    
    # Load and display the results
    try:
        with open('ols_results.json', 'r') as f:
            results = json.load(f)
        
        print("\n📊 REGRESSION RESULTS:")
        print("-" * 30)
        
        # Display in the exact format requested
        ols_debug = results['olsDebug']
        
        print(f"Number of Observations: {ols_debug['nObs']}")
        print(f"R-squared: {ols_debug['r2']}")
        
        print("\nCoefficients:")
        for var, coeff in ols_debug['coeffs'].items():
            print(f"  {var}: {coeff}")
        
        print("\nT-Statistics:")
        for var, tstat in ols_debug['tStats'].items():
            print(f"  {var}: {tstat}")
        
        print("\n📋 OUTPUT FORMAT (as requested):")
        print("-" * 40)
        output_format = {
            "olsDebug": ols_debug
        }
        print(json.dumps(output_format, indent=2))
        
        print("\n📈 INTERPRETATION:")
        print("-" * 20)
        print("• Area coefficient: Each additional sq ft increases price by $156")
        print("• Bedrooms coefficient: Each additional bedroom decreases price by $58,957")
        print("  (This negative coefficient suggests multicollinearity with area)")
        print("• Bathrooms coefficient: Each additional bathroom increases price by $32,347")
        print("• Year Built coefficient: Each year newer increases price by $222")
        print(f"• Model explains {ols_debug['r2']*100}% of price variation (R² = {ols_debug['r2']})")
        
        print("\n✅ SUCCESS: OLS regression completed successfully!")
        print("   All results are in the exact format specified in the requirements.")
        
    except FileNotFoundError:
        print("❌ ERROR: ols_results.json not found. Please run the regression first.")
    except Exception as e:
        print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    main()
