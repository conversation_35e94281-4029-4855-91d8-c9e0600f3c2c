{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# OLS Regression Analysis for Real Estate Data with Time Adjustment\n", "\n", "This notebook performs Ordinary Least Squares (OLS) regression on real estate market data with time adjustment using CSI (Case-Shiller Index).\n", "\n", "**Key Features:**\n", "- Time adjustment using CSI: `timeAdjPrice = comp_close_price × (CSI_t0 / CSI_tsale)`\n", "- Predicts property prices based on: area, bedrooms, bathrooms, year built\n", "- Output format matches API specification with coefficients, t-statistics, R², and observations\n", "\n", "**Process:**\n", "1. Load and examine real estate data\n", "2. Apply time adjustment to comparable sales\n", "3. Extract features and run OLS regression\n", "4. Generate results in specified format"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd\n", "import numpy as np\n", "import statsmodels.api as sm\n", "from statsmodels.stats.diagnostic import het_breuschpagan\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, date\n", "\n", "# Set display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', None)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load and Examine Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the JSON data\n", "with open('sample_data.json', 'r') as f:\n", "    data = json.load(f)\n", "\n", "print(\"Data structure:\")\n", "print(f\"Keys: {list(data.keys())}\")\n", "print(f\"Number of market comps: {len(data['marketComps'])}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Time Adjustment Functions\n", "\n", "Before running OLS regression, we apply time adjustment to comparable sales using CSI (Case-Shiller Index).\n", "Formula: `timeAdjPrice = comp_close_price × (CSI_t0 / CSI_tsale)`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_csi_values():\n", "    \"\"\"\n", "    Get CSI (Case-Shiller Index) values for time adjustment.\n", "    In a real implementation, this would fetch from a CSI data source.\n", "    For demo purposes, we'll use mock data with realistic CSI trends.\n", "    \"\"\"\n", "    # Mock CSI data - in practice, this would come from an external source\n", "    # Using realistic CSI values for Charlotte/Huntersville area (approximate)\n", "    csi_data = {\n", "        '2024-12': 320.5,\n", "        '2025-01': 322.1,\n", "        '2025-02': 323.8,\n", "        '2025-03': 325.2,\n", "        '2025-04': 326.9,\n", "        '2025-05': 328.4,\n", "        '2025-06': 329.8,  # Current reference point (t0)\n", "    }\n", "    return csi_data\n", "\n", "def apply_time_adjustment(close_price, close_date, reference_date=None, csi_data=None):\n", "    \"\"\"\n", "    Apply time adjustment to comparable sale prices using CSI.\n", "    Formula: timeAdjPrice = comp_close_price × (CSI_t0 / CSI_tsale)\n", "    \n", "    Args:\n", "        close_price: Original close price\n", "        close_date: Date of the sale (string format: \"YYYY-MM-DD HH:MM:SS\")\n", "        reference_date: Reference date for adjustment (defaults to current date)\n", "        csi_data: Dictionary of CSI values by month\n", "    \n", "    Returns:\n", "        Adjusted price\n", "    \"\"\"\n", "    if csi_data is None:\n", "        csi_data = get_csi_values()\n", "    \n", "    if reference_date is None:\n", "        reference_date = \"2025-06-01\"  # Current reference point\n", "    \n", "    try:\n", "        # Parse the close date\n", "        sale_date = datetime.strptime(close_date.split()[0], \"%Y-%m-%d\")\n", "        ref_date = datetime.strptime(reference_date, \"%Y-%m-%d\")\n", "        \n", "        # Get month keys for CSI lookup\n", "        sale_month_key = sale_date.strftime(\"%Y-%m\")\n", "        ref_month_key = ref_date.strftime(\"%Y-%m\")\n", "        \n", "        # Get CSI values\n", "        csi_sale = csi_data.get(sale_month_key)\n", "        csi_ref = csi_data.get(ref_month_key)\n", "        \n", "        if csi_sale is None or csi_ref is None:\n", "            print(f\"Warning: CSI data not available for {sale_month_key} or {ref_month_key}, using unadjusted price\")\n", "            return close_price\n", "        \n", "        # Apply time adjustment formula\n", "        adjusted_price = close_price * (csi_ref / csi_sale)\n", "        \n", "        return adjusted_price\n", "        \n", "    except Exception as e:\n", "        print(f\"Error in time adjustment for date {close_date}: {e}\")\n", "        return close_price\n", "\n", "# Test the time adjustment function\n", "print(\"CSI Data:\")\n", "csi_data = get_csi_values()\n", "for month, csi in csi_data.items():\n", "    print(f\"  {month}: {csi}\")\n", "\n", "# Example time adjustment\n", "test_price = 400000\n", "test_date = \"2025-02-28 00:00:00\"\n", "adjusted_price = apply_time_adjustment(test_price, test_date)\n", "adjustment_factor = adjusted_price / test_price\n", "print(f\"\\nExample: ${test_price:,} from {test_date.split()[0]} → ${adjusted_price:,.0f} (factor: {adjustment_factor:.4f})\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Extract Features with Time Adjustment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_comp_features(market_comps, apply_time_adj=True):\n", "    \"\"\"\n", "    Extract features from market comparables data with optional time adjustment\n", "    \n", "    Args:\n", "        market_comps: List of comparable sales data\n", "        apply_time_adj: Whether to apply time adjustment using CSI\n", "    \"\"\"\n", "    comp_data = []\n", "    csi_data = get_csi_values() if apply_time_adj else None\n", "    \n", "    for comp in market_comps:\n", "        try:\n", "            # Extract features\n", "            area = comp.get('comp_property_area')\n", "            bedrooms = comp.get('comp_property_bedrooms')\n", "            baths_full = comp.get('comp_property_bathsFull')\n", "            baths_half = comp.get('comp_property_bathsHalf')\n", "            year_built = comp.get('comp_property_yearBuilt')\n", "            close_price = comp.get('comp_close_price')\n", "            close_date = comp.get('comp_close_date')\n", "            \n", "            # Convert to numeric and handle missing values\n", "            if all(v is not None for v in [area, bedrooms, baths_full, baths_half, year_built, close_price]):\n", "                area = float(area)\n", "                bedrooms = int(bedrooms)\n", "                baths_full = int(baths_full)\n", "                baths_half = int(baths_half)\n", "                year_built = int(year_built)\n", "                close_price = float(close_price)\n", "                \n", "                # Apply time adjustment if requested and close_date is available\n", "                if apply_time_adj and close_date:\n", "                    adjusted_price = apply_time_adjustment(close_price, close_date, csi_data=csi_data)\n", "                    original_price = close_price\n", "                    close_price = adjusted_price\n", "                else:\n", "                    original_price = close_price\n", "                \n", "                # Calculate total bathrooms\n", "                total_baths = baths_full + (baths_half * 0.5)\n", "                \n", "                comp_data.append({\n", "                    'area': area,\n", "                    'beds': bedrooms,\n", "                    'baths': total_baths,\n", "                    'yearBuilt': year_built,\n", "                    'price': close_price,\n", "                    'original_price': original_price,\n", "                    'close_date': close_date,\n", "                    'address': comp.get('comp_property_address', 'Unknown')\n", "                })\n", "        except (ValueError, TypeError) as e:\n", "            print(f\"Skipping comp due to data issue: {e}\")\n", "            continue\n", "    \n", "    return pd.DataFrame(comp_data)\n", "\n", "# Extract comparable sales data with time adjustment\n", "print(\"Extracting comparable sales with time adjustment...\")\n", "df_comps = extract_comp_features(data['marketComps'], apply_time_adj=True)\n", "print(f\"✓ Extracted {len(df_comps)} valid comparable sales\")\n", "\n", "# Show time adjustment summary\n", "if len(df_comps) > 0:\n", "    total_adjustment = df_comps['price'].sum() - df_comps['original_price'].sum()\n", "    avg_adjustment_pct = ((df_comps['price'] / df_comps['original_price'] - 1) * 100).mean()\n", "    print(f\"✓ Average time adjustment: {avg_adjustment_pct:.2f}%\")\n", "    print(f\"✓ Total price adjustment: ${total_adjustment:,.0f}\")\n", "    \n", "    print(\"\\nFirst few records with time adjustment:\")\n", "    display_cols = ['area', 'beds', 'baths', 'yearBuilt', 'original_price', 'price', 'close_date']\n", "    print(df_comps[display_cols].head())"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 4}